"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var express_1 = require("express");
var path = require("path");
var app = (0, express_1.default)();
app.use(express_1.default.json());
app.use(express_1.default.static(path.join(__dirname, "dist")));
app.post("/login", function (req, res) {
    console.log(req.body);
    res.status(200).send("ok");
});
var PORT = process.env.PORT || 3000;
app.listen(PORT, function () {
    console.log("Server is running on port ".concat(PORT));
});
