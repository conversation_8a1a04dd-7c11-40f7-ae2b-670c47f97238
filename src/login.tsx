const Login: React.FC = () => {

  // function onSubmit(e: React.FormEvent<HTMLFormElement>) {
  //   e.preventDefault();
  // }
  return (
    <div>
      <form action="/login">
        <input type="text" name="username" placeholder="Username" />
        <input type="text" name="roomname" placeholder="Room" />
        <button type="submit">Login</button>
      </form>
    </div>
  );
}
export default Login;
