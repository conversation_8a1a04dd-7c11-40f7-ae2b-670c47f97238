import express, { Request, Response } from "express";
import * as path from "path";
const app = express();

app.use(express.json());
app.use(express.static(path.join(__dirname, "dist")));
app.post("/login", (req: Request, res: Response) => {
  console.log(req.body);
  res.status(200).send("ok");
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
